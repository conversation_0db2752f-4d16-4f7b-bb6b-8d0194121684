{"name": "@apps/admin", "type": "module", "version": "1.0.0", "scripts": {"dev": "vite --mode dev", "test": "vite --mode test", "dev:prod": "vite --mode prod", "build": "vite build --mode prod", "build:test": "vite build --mode test", "preview": "vite preview", "typecheck": "vue-tsc --noEmit -p tsconfig.json --incremental false --composite false --diagnostics", "lint:fix": "eslint . --fix --quiet"}, "dependencies": {"@sa/axios": "workspace:*", "@sa/color": "workspace:*", "@sa/directives": "workspace:*", "@sa/hooks": "workspace:*", "@sa/materials": "workspace:*", "@sa/utils": "workspace:*", "@yw/fabric": "workspace:*"}}