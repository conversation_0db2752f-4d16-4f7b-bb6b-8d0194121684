/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AnimationCard: typeof import('./../../../../packages/base/components/common/chat/AnimationCard.vue')['default']
    AppProvider: typeof import('./../../../../packages/base/components/common/app-provider.vue')['default']
    AudioMessage: typeof import('./../../../../packages/base/components/common/chat/message-components/AudioMessage.vue')['default']
    BackButton: typeof import('./../../../../packages/base/components/common/back-button.vue')['default']
    BetterScroll: typeof import('./../../../../packages/base/components/custom/better-scroll.vue')['default']
    BubbleList: typeof import('./../../../../packages/base/components/common/chat/BubbleList.vue')['default']
    ButtonIcon: typeof import('./../../../../packages/base/components/custom/button-icon.vue')['default']
    ChatContainer: typeof import('./../../../../packages/base/components/common/chat/ChatContainer.vue')['default']
    ChatSender: typeof import('./../../../../packages/base/components/common/chat/ChatSender.vue')['default']
    CodeCard: typeof import('./../../../../packages/base/components/common/code/CodeCard.vue')['default']
    CountTo: typeof import('./../../../../packages/base/components/custom/count-to.vue')['default']
    DarkModeContainer: typeof import('./../../../../packages/base/components/common/dark-mode-container.vue')['default']
    ExceptionBase: typeof import('./../../../../packages/base/components/common/exception-base.vue')['default']
    FileMessage: typeof import('./../../../../packages/base/components/common/chat/message-components/FileMessage.vue')['default']
    FilesSelect: typeof import('./../../../../packages/base/components/common/chat/FilesSelect.vue')['default']
    FileUpload: typeof import('./../../../../packages/base/components/common/file-upload.vue')['default']
    'FileUpload.example': typeof import('./../components/common/FileUpload.example.vue')['default']
    FullScreen: typeof import('./../../../../packages/base/components/common/full-screen.vue')['default']
    IconAntDesignEnterOutlined: typeof import('~icons/ant-design/enter-outlined')['default']
    IconAntDesignReloadOutlined: typeof import('~icons/ant-design/reload-outlined')['default']
    IconAntDesignSettingOutlined: typeof import('~icons/ant-design/setting-outlined')['default']
    IconGridiconsFullscreen: typeof import('~icons/gridicons/fullscreen')['default']
    IconGridiconsFullscreenExit: typeof import('~icons/gridicons/fullscreen-exit')['default']
    'IconIc:roundPlus': typeof import('~icons/ic/round-plus')['default']
    IconIcRoundClose: typeof import('~icons/ic/round-close')['default']
    IconIcRoundDelete: typeof import('~icons/ic/round-delete')['default']
    IconIcRoundPlus: typeof import('~icons/ic/round-plus')['default']
    IconIcRoundRefresh: typeof import('~icons/ic/round-refresh')['default']
    IconIcRoundRemove: typeof import('~icons/ic/round-remove')['default']
    IconIcRoundSearch: typeof import('~icons/ic/round-search')['default']
    IconLocalCodePlus: typeof import('~icons/local/code-plus')['default']
    IconLocalEpPlus: typeof import('~icons/local/ep-plus')['default']
    IconLocalIcAi: typeof import('~icons/local/ic-ai')['default']
    IconLocalImageOutline: typeof import('~icons/local/image-outline')['default']
    IconLocalLogo: typeof import('~icons/local/logo')['default']
    IconLocalTitle: typeof import('~icons/local/title')['default']
    IconMdiArrowDownThin: typeof import('~icons/mdi/arrow-down-thin')['default']
    IconMdiArrowUpThin: typeof import('~icons/mdi/arrow-up-thin')['default']
    IconMdiDrag: typeof import('~icons/mdi/drag')['default']
    IconMdiKeyboardEsc: typeof import('~icons/mdi/keyboard-esc')['default']
    IconMdiKeyboardReturn: typeof import('~icons/mdi/keyboard-return')['default']
    IconMdiRefresh: typeof import('~icons/mdi/refresh')['default']
    IconUilSearch: typeof import('~icons/uil/search')['default']
    ImageMessage: typeof import('./../../../../packages/base/components/common/chat/message-components/ImageMessage.vue')['default']
    List: typeof import('./../../../../packages/base/components/common/list/List.vue')['default']
    LookForward: typeof import('./../../../../packages/base/components/custom/look-forward.vue')['default']
    MDEditor: typeof import('./../../../../packages/base/components/common/md-editor/MDEditor.vue')['default']
    MenuToggler: typeof import('./../../../../packages/base/components/common/menu-toggler.vue')['default']
    NBackTop: typeof import('naive-ui')['NBackTop']
    NBreadcrumb: typeof import('naive-ui')['NBreadcrumb']
    NBreadcrumbItem: typeof import('naive-ui')['NBreadcrumbItem']
    NButton: typeof import('naive-ui')['NButton']
    NCard: typeof import('naive-ui')['NCard']
    NCheckbox: typeof import('naive-ui')['NCheckbox']
    NCollapse: typeof import('naive-ui')['NCollapse']
    NCollapseItem: typeof import('naive-ui')['NCollapseItem']
    NColorPicker: typeof import('naive-ui')['NColorPicker']
    NDataTable: typeof import('naive-ui')['NDataTable']
    NDatePicker: typeof import('naive-ui')['NDatePicker']
    NDialogProvider: typeof import('naive-ui')['NDialogProvider']
    NDivider: typeof import('naive-ui')['NDivider']
    NDrawer: typeof import('naive-ui')['NDrawer']
    NDrawerContent: typeof import('naive-ui')['NDrawerContent']
    NDropdown: typeof import('naive-ui')['NDropdown']
    NDynamicInput: typeof import('naive-ui')['NDynamicInput']
    NEmpty: typeof import('naive-ui')['NEmpty']
    NForm: typeof import('naive-ui')['NForm']
    NFormItem: typeof import('naive-ui')['NFormItem']
    NFormItemGi: typeof import('naive-ui')['NFormItemGi']
    NGrid: typeof import('naive-ui')['NGrid']
    NGridItem: typeof import('naive-ui')['NGridItem']
    NImage: typeof import('naive-ui')['NImage']
    NInput: typeof import('naive-ui')['NInput']
    NInputGroup: typeof import('naive-ui')['NInputGroup']
    NInputNumber: typeof import('naive-ui')['NInputNumber']
    NLoadingBarProvider: typeof import('naive-ui')['NLoadingBarProvider']
    NMenu: typeof import('naive-ui')['NMenu']
    NMessageProvider: typeof import('naive-ui')['NMessageProvider']
    NModal: typeof import('naive-ui')['NModal']
    NNotificationProvider: typeof import('naive-ui')['NNotificationProvider']
    NP: typeof import('naive-ui')['NP']
    NPopconfirm: typeof import('naive-ui')['NPopconfirm']
    NPopover: typeof import('naive-ui')['NPopover']
    NProgress: typeof import('naive-ui')['NProgress']
    NQrCode: typeof import('naive-ui')['NQrCode']
    NRadio: typeof import('naive-ui')['NRadio']
    NRadioGroup: typeof import('naive-ui')['NRadioGroup']
    NResult: typeof import('naive-ui')['NResult']
    NScrollbar: typeof import('naive-ui')['NScrollbar']
    NSelect: typeof import('naive-ui')['NSelect']
    NSpace: typeof import('naive-ui')['NSpace']
    NSpin: typeof import('naive-ui')['NSpin']
    NStep: typeof import('naive-ui')['NStep']
    NSteps: typeof import('naive-ui')['NSteps']
    NSwitch: typeof import('naive-ui')['NSwitch']
    NTab: typeof import('naive-ui')['NTab']
    NTabs: typeof import('naive-ui')['NTabs']
    NTag: typeof import('naive-ui')['NTag']
    NText: typeof import('naive-ui')['NText']
    NTooltip: typeof import('naive-ui')['NTooltip']
    NTree: typeof import('naive-ui')['NTree']
    NTreeSelect: typeof import('naive-ui')['NTreeSelect']
    NUpload: typeof import('naive-ui')['NUpload']
    NUploadDragger: typeof import('naive-ui')['NUploadDragger']
    NWatermark: typeof import('naive-ui')['NWatermark']
    PinToggler: typeof import('./../../../../packages/base/components/common/pin-toggler.vue')['default']
    PreviewCode: typeof import('./../../../../packages/base/components/common/code/PreviewCode.vue')['default']
    ReloadButton: typeof import('./../../../../packages/base/components/common/reload-button.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SoybeanAvatar: typeof import('./../../../../packages/base/components/custom/soybean-avatar.vue')['default']
    SvgIcon: typeof import('./../../../../packages/base/components/custom/svg-icon.vue')['default']
    SystemLogo: typeof import('./../../../../packages/base/components/common/system-logo.vue')['default']
    TableColumnSetting: typeof import('./../../../../packages/base/components/advanced/table-column-setting.vue')['default']
    TableHeaderOperation: typeof import('./../../../../packages/base/components/advanced/table-header-operation.vue')['default']
    ThemeSchemaSwitch: typeof import('./../../../../packages/base/components/common/theme-schema-switch.vue')['default']
    VoicePlayButton: typeof import('./../../../../packages/base/components/common/chat/VoicePlayButton.vue')['default']
    WaveBg: typeof import('./../../../../packages/base/components/custom/wave-bg.vue')['default']
  }
}
