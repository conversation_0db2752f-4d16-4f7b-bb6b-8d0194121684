/* @jsxImportSource vue */
import { computed, defineComponent } from 'vue'
import styles from './index.module.css'
// import { useForm } from '@/hooks'

export default defineComponent({
  name: 'SingleChoice',
  props: {
    options: {
      type: Array as () => Array<{ label: string, value: any }>,
      required: true,
    },
    modelValue: {
      type: [String, Number],
      default: '',
    },
    disabled: {
      type: Boolean,
      default: undefined,
    },
  },
  emits: ['update:modelValue', 'image-click'],
  setup(props, { emit }) {
    const modelValue = computed({
      get() {
        return props.modelValue
      },
      set(value) {
        emit('update:modelValue', value)
      },
    })

    // const { mergedDisabled } = useForm({
    //   disabled: computed(() => props.disabled),
    // })

    const handleChange = (value: any) => {
      // if (mergedDisabled.value)
      //   return
      modelValue.value = value
    }

    return () => {
      return (
        <ul class={styles.singleChoice}>
          {props.options.map(option => (
            <li>
              <label key={option.value} class={styles.choiceItem}>
                <div
                  class={[
                    styles.choiceItemQn,
                    modelValue.value === option.value ? styles.choiceItemQnChecked : '',
                  ]}
                  onClick={() => {
                    // if (mergedDisabled.value) {
                    //   return
                    // }
                    handleChange(option.value)
                  }}
                >
                  <span>{option.value}</span>
                </div>
                <span
                  class={[styles.choiceItemLabel, 'contents']}
                  v-html={option.label}
                  v-katex
                />
              </label>
            </li>
          ))}
        </ul>
      )
    }
  },
})
