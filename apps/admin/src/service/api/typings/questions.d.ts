declare namespace QuestionsApi {
/**
 * UwooAgent.Model.AI.GetAgentModelInfoOutput，获取智能体模型信息输出
 */
  interface AgentModelInfoResponse {
    /**
     * Id
     */
    Id: string
    /**
     * 是否支持上下文
     */
    IsContext: boolean
    /**
     * 模型描述
     */
    ModelDescribe: string
    /**
     * 模型名称
     */
    ModelName: string
  }

  /**
   * UwooAgent.Model.AI.CreateQuestionInput，创建题目请求参数
   */
  interface CreateQuestionRequest {
    /**
     * 补充内容（出题要求）
     */
    AdditionalRequirements: string
    /**
     * Ai模型Id
     */
    AIModeId: string
    /**
     * 传 单元-章节名称（章节出题时使用）
     */
    ChapterIds: string[]
    /**
     * 难度等级名称
     */
    DifficultyLevelName: string | null
    /**
     * 文件出题时使用的文件URL（附件出题时使用）
     */
    FileUrls: string[]
    /**
     * 年级
     */
    Grade: number
    /**
     * 知识点ID列表（知识点出题时使用）
     */
    KnowledgePointIds: string[]
    /**
     * 出题模式
     */
    Mode: 1 | 2 | 3 | 4
    /**
     * 出题数量
     */
    QuestionCount: number | null
    /**
     * 出题方向名称（也就是学习水平）
     */
    QuestionDirectionName: string | null
    /**
     * 题型ID列表（可多选）
     */
    QuestionTypeIds: number[] | null

    /**
     * 出题范围文本（文本出题时使用）
     */
    TextContent: string

  }

  interface GetDifficultyResponse {
    code: string
    text: string
    value: string
  }
  /**
   * Uwoo.Business.Base_Manage.Sys_ExamineListTreeDTO
   */
  interface GetLearningLevelResponse {
    children?: null | []
    Children?: any[] | null
    Id: string
    key: string
    Level: number | null
    ParentId: null | string
    Text: string
    title: string
    value: string
    Value: string
  }
  /**
   * UwooAgent.Model.AI.QuestionTypeDto，题型数据传输对象
   */
  interface GetQuestionTypesResponse {
    /**
     * 题型描述
     */
    Description: string
    /**
     * 题型ID
     */
    Id: number
    /**
     * 题型名称
     */
    Name: string
  }

  interface GetChapterListResponseRequest {
    /**
     * 年级
     */
    grade: string
    /**
     * 学期
     */
    term: number
    /**
     * 学年
     */
    year: number
  }

  /**
   * 根据学年，学期获取章节数据
   */
  interface GetChapterListResponse {
    ChapterId: string
    ChapterName: string
    ParentId: string
    Second: ChapterListItem[] | null
  }

  interface ChapterListItem {
    ChapterId: string
    ChapterName: string
    ParentId: string
    ResourceCount: number
    Third: string | null
  }
}
