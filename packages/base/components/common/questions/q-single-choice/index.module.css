.singleChoice {

  .choiceItem {
    display: flex;
    align-items: center;
    min-height: 44px;
    margin-bottom: 12px;

    .choiceItemLabel {
      display: inline-flex;
    }

    .choiceItemQn {
      width: 30px;
      height: 30px;
      background: #FFFFFF;
      border: 1px solid #A3A3A3;
      text-align: center;
      border-radius: 100%;
      margin-right: 12px;
      font-weight: 500;
      color: #A2A2A2;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      flex-shrink: 0;
    }

    .choiceItemQnChecked {
      background-color: #73CFFB;
      color: #FFFFFF;
      border: 1px solid #73CFFB;
    }
  }

}

