import { qsFormInjectionKey } from 'packages/base/components/common/questions/qs-provide/context.ts'

interface Options {
  disabled: Ref<boolean | null | undefined>
}

export function useForm(options: Options) {
  // 选中的选项
  const QForm = inject(qsFormInjectionKey, null)

  const disabled = computed(() => options.disabled.value)

  const mergedDisabled = computed(() => {
    if (disabled.value !== undefined && disabled.value !== null)
      return disabled.value
    return QForm?.props?.disabled ?? false
  })

  // 键盘类型
  const keyboardType = computed(() => {
    return QForm?.props.keyboardType ?? 'system'
  })

  return {
    mergedDisabled,
    keyboardType,
    keyboardRef: QForm!.keyboardRef,
  }
}
