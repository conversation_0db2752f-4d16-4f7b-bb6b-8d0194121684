import * as process from 'node:process'
import * as path from 'node:path'
import { defineConfig, loadEnv } from 'vite'
import { setupVitePlugins } from './plugins'
import { createViteProxy, getBuildTime } from './config'

export default defineConfig((configEnv) => {
  const viteEnv = loadEnv(
    configEnv.mode,
    process.cwd(),
  ) as unknown as Env.ImportMeta

  const buildTime = getBuildTime()

  const enableProxy = configEnv.command === 'serve' && !configEnv.isPreview
  return {
    base: viteEnv.VITE_BASE_URL,
    resolve: {
      alias: {
        '@sa/components': path.resolve(
          process.cwd(),
          '../../packages/base/components',
        ),
        '@sa/store': path.resolve(
          process.cwd(),
          '../../packages/base/store',
        ),
        '@sa/enum': path.resolve(
          process.cwd(),
          '../../packages/base/enum',
        ),
        '@sa/constants': path.resolve(
          process.cwd(),
          '../../packages/base/constants',
        ),
        '@sa/styles': path.resolve(
          process.cwd(),
          '../../packages/base/styles',
        ),
        '@sa/service': path.resolve(
          process.cwd(),
          '../../packages/base/service',
        ),
        '@sa/theme': path.resolve(
          process.cwd(),
          '../../packages/base/theme',
        ),
        '@sa/directives': path.resolve(
          process.cwd(),
          '../../packages/directives',
        ),
        '~': path.resolve(process.cwd(), ''),
        '@': path.resolve(process.cwd(), 'src'),
      },
    },
    css: {
      preprocessorOptions: {
        scss: {
          api: 'modern-compiler',
          additionalData: `@use "@sa/styles/scss/global.scss" as *;`,
        },
      },
    },
    plugins: setupVitePlugins(viteEnv, buildTime),
    define: {
      BUILD_TIME: JSON.stringify(buildTime),
    },
    server: {
      host: '0.0.0.0',
      port: 9527,
      open: true,
      proxy: createViteProxy(viteEnv, enableProxy),
    },
    preview: {
      port: 9725,
    },
    build: {
      outDir: './dist',
      reportCompressedSize: false,
      sourcemap: viteEnv.VITE_SOURCE_MAP === 'Y',
      commonjsOptions: {
        ignoreTryCatch: false,
      },
      rollupOptions: {
        output: {
          entryFileNames: 'static/js/[name]-[hash].js', // 包的入口文件名称
          chunkFileNames: 'static/js/[name]-[hash].js', // 引入文件名的名称
          assetFileNames: 'static/[ext]/[name]-[hash].[ext]', // 资源文件像 字体，图片等
          manualChunks(id: string) {
            if (id.includes('node_modules/vue-element-plus-x')) {
              return 'vue-element-plus-x'
            }
            if (id.includes('node_modules')) {
              return 'vendor'
            }
          },
        },
      },
    },
    esbuild: {
      drop: configEnv.mode === 'production' ? ['console', 'debugger'] : [],
    },
  }
})
